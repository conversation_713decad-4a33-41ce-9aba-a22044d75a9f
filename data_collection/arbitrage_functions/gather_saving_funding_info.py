#!/usr/bin/env python3
"""
Gather saving funding info - combines savings, spot, perpetual, and funding rate data.
Simplified version for the new data structure with clean symbols.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from get_root_symbol import add_base_symbol_multiplier


def load_savings_data():
    """Load processed savings data"""
    savings_file = Path("historical_data/savings_info_df/savings_info.csv")
    
    if not savings_file.exists():
        print(f"Error: Savings data file not found: {savings_file}")
        return None
    
    try:
        savings_df = pd.read_csv(savings_file)
        print(f"Loaded savings data: {len(savings_df)} records")
        print(f"Savings data columns: {list(savings_df.columns)}")
        return savings_df
    except Exception as e:
        print(f"Error loading savings data: {e}")
        return None


def load_snapshot_data_with_time_matching(file_path, data_name, reference_timestamp=None, time_tolerance_hours=6):
    """
    Generic function to load snapshot data with flexible timestamp matching

    Args:
        file_path: Path to the snapshot CSV file
        data_name: Name of the data type for logging
        reference_timestamp: Reference timestamp to match against
        time_tolerance_hours: Maximum time difference allowed for matching
    """
    if not file_path.exists():
        print(f"Warning: {data_name} file not found: {file_path}")
        return None

    try:
        df = pd.read_csv(file_path)
        print(f"Loaded {data_name}: {len(df)} records")

        if 'timestamp' not in df.columns:
            print(f"Warning: No timestamp column in {data_name}")
            return df

        # Convert timestamp to datetime
        df['timestamp'] = pd.to_datetime(df['timestamp'])

        if reference_timestamp is not None:
            # Try time-aware matching
            reference_dt = pd.to_datetime(reference_timestamp)
            time_diff = abs(df['timestamp'] - reference_dt)
            tolerance = pd.Timedelta(hours=time_tolerance_hours)

            within_tolerance = df[time_diff <= tolerance]

            if not within_tolerance.empty:
                # Use closest timestamp within tolerance
                closest_idx = time_diff[time_diff <= tolerance].idxmin()
                closest_timestamp = df.loc[closest_idx, 'timestamp']
                df = df[df['timestamp'] == closest_timestamp].copy()
                print(f"{data_name}: Using timestamp {closest_timestamp} (within tolerance)")
            else:
                # Fall back to latest
                latest_timestamp = df['timestamp'].max()
                df = df[df['timestamp'] == latest_timestamp].copy()
                print(f"{data_name}: Using latest timestamp {latest_timestamp} (no data within tolerance)")
        else:
            # Use latest timestamp
            latest_timestamp = df['timestamp'].max()
            df = df[df['timestamp'] == latest_timestamp].copy()
            print(f"{data_name}: Using latest timestamp {latest_timestamp}")

        print(f"{data_name}: Selected {len(df)} records")
        return df

    except Exception as e:
        print(f"Error loading {data_name}: {e}")
        return None


def load_spot_data(reference_timestamp=None, time_tolerance_hours=6):
    """Load spot snapshot data with optional time matching"""
    return load_snapshot_data_with_time_matching(
        Path("snapshot_data/spot_snapshot/spot_snapshot.csv"),
        "spot data",
        reference_timestamp,
        time_tolerance_hours
    )


def load_perp_data(reference_timestamp=None, time_tolerance_hours=6):
    """Load perpetual snapshot data with optional time matching"""
    return load_snapshot_data_with_time_matching(
        Path("snapshot_data/perp_snapshot/perp_snapshot.csv"),
        "perpetual data",
        reference_timestamp,
        time_tolerance_hours
    )


def load_funding_data():
    """Load funding rates data and process base_symbol"""
    funding_file = Path("historical_data/funding_info_df/funding_info.csv")

    if not funding_file.exists():
        print(f"Warning: Funding data file not found: {funding_file}")
        return None

    try:
        funding_df = pd.read_csv(funding_file)
        print(f"Loaded funding data: {len(funding_df)} records")
        print(f"Funding data columns: {list(funding_df.columns)}")

        # Add base_symbol and multiplier using get_root_symbol
        print("Processing funding data to extract base_symbol...")
        funding_df = add_base_symbol_multiplier(funding_df)
        print(f"After processing: columns = {list(funding_df.columns)}")
        print(f"Sample base_symbols: {funding_df['base_symbol'].unique()[:10]}")

        return funding_df
    except Exception as e:
        print(f"Error loading funding data: {e}")
        return None


def load_funding_rates_snapshot(reference_timestamp=None, time_tolerance_hours=6):
    """
    Load current funding rates snapshot data with flexible timestamp matching

    Args:
        reference_timestamp: Reference timestamp to match against (e.g., from savings data)
        time_tolerance_hours: Maximum time difference allowed for matching (default: 6 hours)
    """
    snapshot_file = Path("snapshot_data/funding_rates_snapshot/funding_rates_snapshot.csv")

    if not snapshot_file.exists():
        print(f"Warning: Funding rates snapshot file not found: {snapshot_file}")
        return None

    try:
        snapshot_df = pd.read_csv(snapshot_file)
        print(f"Loaded funding rates snapshot: {len(snapshot_df)} records")
        print(f"Snapshot data columns: {list(snapshot_df.columns)}")

        # Convert timestamp to datetime for comparison
        snapshot_df['timestamp'] = pd.to_datetime(snapshot_df['timestamp'])

        if reference_timestamp is not None:
            # Try to find data within time tolerance of reference timestamp
            reference_dt = pd.to_datetime(reference_timestamp)
            time_diff = abs(snapshot_df['timestamp'] - reference_dt)
            tolerance = pd.Timedelta(hours=time_tolerance_hours)

            # Filter data within tolerance
            within_tolerance = snapshot_df[time_diff <= tolerance]

            if not within_tolerance.empty:
                # Use the closest timestamp within tolerance
                closest_idx = time_diff[time_diff <= tolerance].idxmin()
                closest_timestamp = snapshot_df.loc[closest_idx, 'timestamp']
                snapshot_df = snapshot_df[snapshot_df['timestamp'] == closest_timestamp].copy()
                print(f"Using timestamp {closest_timestamp} (within {time_tolerance_hours}h of reference)")
            else:
                # Fall back to latest timestamp if no data within tolerance
                latest_timestamp = snapshot_df['timestamp'].max()
                snapshot_df = snapshot_df[snapshot_df['timestamp'] == latest_timestamp].copy()
                print(f"No data within {time_tolerance_hours}h tolerance, using latest: {latest_timestamp}")
        else:
            # No reference timestamp provided, use latest
            latest_timestamp = snapshot_df['timestamp'].max()
            snapshot_df = snapshot_df[snapshot_df['timestamp'] == latest_timestamp].copy()
            print(f"Using latest snapshot timestamp: {latest_timestamp}")

        print(f"Selected {len(snapshot_df)} records for matching")
        return snapshot_df

    except Exception as e:
        print(f"Error loading funding rates snapshot: {e}")
        return None


def combine_savings_with_market_data():
    """
    Combine savings data with spot, perpetual, and funding rate data.
    Simplified version using direct symbol matching.
    """

    # Load savings data
    savings_df = load_savings_data()
    if savings_df is None:
        return None

    # Get reference timestamp from savings data for time-aware matching
    reference_timestamp = None
    if not savings_df.empty and 'timestamp' in savings_df.columns:
        reference_timestamp = savings_df['timestamp'].max()
        print(f"Using reference timestamp from savings data: {reference_timestamp}")

    # Load market data with time-aware matching
    spot_df = load_spot_data(
        reference_timestamp=reference_timestamp,
        time_tolerance_hours=6
    )
    perp_df = load_perp_data(
        reference_timestamp=reference_timestamp,
        time_tolerance_hours=6
    )
    funding_df = load_funding_data()
    funding_snapshot_df = load_funding_rates_snapshot(
        reference_timestamp=reference_timestamp,
        time_tolerance_hours=6  # Allow 6 hours tolerance for matching
    )
    
    # Start with savings data as the base
    result_df = savings_df.copy()
    
    # Add base_symbol and multiplier columns to savings data
    result_df = add_base_symbol_multiplier(result_df)
    
    print(f"\nStarting with {len(result_df)} savings records")
    print(f"Unique symbols in savings: {result_df['symbol'].nunique()}")
    
    # Join with spot data using symbol directly
    if spot_df is not None:
        # Extract base_symbol from spot data for matching
        if 'base_symbol' in spot_df.columns:
            # Use base_symbol if available
            spot_data = spot_df.groupby(['exchange', 'base_symbol']).agg({
                'last': 'first',
                'turnover_24h': 'first'
            }).reset_index()
            spot_data.rename(columns={'base_symbol': 'symbol'}, inplace=True)
        else:
            # Use symbol directly
            spot_data = spot_df.groupby(['exchange', 'symbol']).agg({
                'last': 'first',
                'turnover_24h': 'first'
            }).reset_index()
        
        result_df = result_df.merge(
            spot_data,
            on=['exchange', 'symbol'],
            how='left',
            suffixes=('', '_spot')
        )
        spot_coverage = result_df['last'].notna().sum()
        print(f"Spot data coverage: {spot_coverage}/{len(result_df)} ({spot_coverage/len(result_df)*100:.1f}%)")
    else:
        result_df['last'] = np.nan
        result_df['turnover_24h'] = np.nan
    
    # Join with perpetual data using symbol directly
    if perp_df is not None:
        # Extract base_symbol from perpetual data for matching
        if 'base_symbol' in perp_df.columns:
            # Use base_symbol if available
            perp_data = perp_df.groupby(['exchange', 'base_symbol']).agg({
                'last': 'first',
                'turnover_24h': 'first'
            }).reset_index()
            perp_data.rename(columns={
                'last': 'perp_last',
                'turnover_24h': 'perp_turnover_24h',
                'base_symbol': 'symbol'
            }, inplace=True)
        else:
            # Use symbol directly
            perp_data = perp_df.groupby(['exchange', 'symbol']).agg({
                'last': 'first',
                'turnover_24h': 'first'
            }).reset_index()
            perp_data.rename(columns={
                'last': 'perp_last',
                'turnover_24h': 'perp_turnover_24h'
            }, inplace=True)
        
        result_df = result_df.merge(
            perp_data,
            on=['exchange', 'symbol'],
            how='left'
        )
        perp_coverage = result_df['perp_last'].notna().sum()
        print(f"Perpetual data coverage: {perp_coverage}/{len(result_df)} ({perp_coverage/len(result_df)*100:.1f}%)")
    else:
        result_df['perp_last'] = np.nan
        result_df['perp_turnover_24h'] = np.nan
    
    # Join with funding data using base_symbol
    if funding_df is not None:
        print("Processing funding data...")

        # Use base_symbol from funding data for matching
        funding_data = funding_df.groupby(['exchange', 'base_symbol']).agg({
            'apr_1d': 'first',
            'apr_2d': 'first',
            'apr_5d': 'first',
            'apr_10d': 'first',
            'apr_20d': 'first'
        }).reset_index()

        # Rename columns for clarity
        funding_data.rename(columns={
            'base_symbol': 'symbol',  # Match with savings symbol
            'apr_1d': 'funding_apr_1d',
            'apr_2d': 'funding_apr_2d',
            'apr_5d': 'funding_apr_5d',
            'apr_10d': 'funding_apr_10d',
            'apr_20d': 'funding_apr_20d'
        }, inplace=True)

        print(f"Funding data after processing: {len(funding_data)} unique (exchange, symbol) pairs")
        print(f"Sample funding symbols: {funding_data['symbol'].unique()[:10]}")

        result_df = result_df.merge(
            funding_data,
            on=['exchange', 'symbol'],
            how='left'
        )
        funding_coverage = result_df['funding_apr_1d'].notna().sum()
        print(f"Funding data coverage: {funding_coverage}/{len(result_df)} ({funding_coverage/len(result_df)*100:.1f}%)")
    else:
        result_df['funding_apr_1d'] = np.nan
        result_df['funding_apr_2d'] = np.nan
        result_df['funding_apr_5d'] = np.nan
        result_df['funding_apr_10d'] = np.nan
        result_df['funding_apr_20d'] = np.nan

    # Join with current funding rates snapshot using base_symbol
    if funding_snapshot_df is not None:
        print("Processing funding rates snapshot...")

        # Use base_symbol from snapshot data for matching
        snapshot_data = funding_snapshot_df.groupby(['exchange', 'base_symbol']).agg({
            'funding_rate': 'first',  # Take first if multiple records
            'timestamp': 'first'      # Keep timestamp for reference
        }).reset_index()

        # Rename columns for clarity
        snapshot_data.rename(columns={
            'base_symbol': 'symbol',  # Match with savings symbol
            'funding_rate': 'current_funding_rate',
            'timestamp': 'funding_rate_timestamp'
        }, inplace=True)

        print(f"Snapshot data after processing: {len(snapshot_data)} unique (exchange, symbol) pairs")
        print(f"Sample snapshot symbols: {snapshot_data['symbol'].unique()[:10]}")

        result_df = result_df.merge(
            snapshot_data,
            on=['exchange', 'symbol'],
            how='left'
        )
        snapshot_coverage = result_df['current_funding_rate'].notna().sum()
        print(f"Current funding rate coverage: {snapshot_coverage}/{len(result_df)} ({snapshot_coverage/len(result_df)*100:.1f}%)")
    else:
        result_df['current_funding_rate'] = np.nan
        result_df['funding_rate_timestamp'] = np.nan
    
    # Calculate profit (savings + funding)
    # Use current funding rate if available, otherwise fall back to 1d average
    result_df['effective_funding_rate'] = result_df['current_funding_rate'].fillna(
        result_df['funding_apr_1d'] / 365 / 3  # Convert annual to 8-hour rate (3 times per day)
    ).fillna(0)

    # Calculate annualized funding rate from current rate (assuming 8-hour intervals, 3 times per day)
    result_df['current_funding_apr'] = result_df['current_funding_rate'].fillna(0) * 365 * 3

    # Calculate profit using current funding rate if available, otherwise use 1d average
    result_df['profit_current'] = result_df['apr_current'] + result_df['current_funding_apr'].fillna(
        result_df['funding_apr_1d'].fillna(0)
    )

    profit_coverage = result_df['profit_current'].notna().sum()
    print(f"Profit calculation coverage: {profit_coverage}/{len(result_df)} ({profit_coverage/len(result_df)*100:.1f}%)")

    # Show funding rate data quality
    current_rate_coverage = result_df['current_funding_rate'].notna().sum()
    historical_rate_coverage = result_df['funding_apr_1d'].notna().sum()
    print(f"Current funding rate coverage: {current_rate_coverage}/{len(result_df)} ({current_rate_coverage/len(result_df)*100:.1f}%)")
    print(f"Historical funding rate coverage: {historical_rate_coverage}/{len(result_df)} ({historical_rate_coverage/len(result_df)*100:.1f}%)")
    
    # Add base_symbol and multiplier columns for display
    def extract_base_symbol_multiplier(symbol):
        """Extract base_symbol and multiplier from clean savings symbols"""
        if not isinstance(symbol, str):
            return symbol, 1
            
        # Check for multiplier prefixes in clean symbols
        if symbol.startswith('1000000'):
            return symbol[7:], 1000000
        elif symbol.startswith('100000'):
            return symbol[6:], 100000
        elif symbol.startswith('10000'):
            return symbol[5:], 10000
        elif symbol.startswith('1000'):
            return symbol[4:], 1000
        elif symbol.startswith('100'):
            return symbol[3:], 100
        elif symbol.startswith('1M'):
            return symbol[2:], 1000000
        else:
            return symbol, 1
    
    # Apply to all symbols
    base_symbol_multiplier = result_df['symbol'].apply(extract_base_symbol_multiplier)
    result_df['base_symbol'] = [x[0] for x in base_symbol_multiplier]
    result_df['multiplier'] = [x[1] for x in base_symbol_multiplier]
    
    return result_df


def main():
    """Main function to combine all data"""
    print("Starting to combine savings with market data...")
    print("Using simplified data structure with clean symbols...")
    
    # Combine all data
    combined_df = combine_savings_with_market_data()
    
    if combined_df is None:
        print("Failed to combine data")
        return
    
    # Create output directory
    output_dir = Path("historical_data/combined_info_df")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save combined data
    output_file = output_dir / "saving_funding_info.csv"
    combined_df.to_csv(output_file, index=False)
    print(f"\nSaved combined data to: {output_file}")
    print(f"Total records: {len(combined_df)}")
    
    # Display summary statistics
    print("\n=== Data Summary ===")
    print(f"Total records: {len(combined_df)}")
    print(f"Exchanges: {combined_df['exchange'].nunique()}")
    print(f"Unique symbols: {combined_df['symbol'].nunique()}")
    print(f"Products: {combined_df['product'].unique()}")
    print(f"Tenors: {combined_df['tenor'].unique()}")
    
    # Show top profit opportunities
    print("\n=== Top Profit Opportunities ===")
    display_columns = ['exchange', 'symbol', 'product', 'tenor', 'apr_current', 'funding_apr_1d', 'profit_current']
    # Only include columns that exist in the dataframe
    available_columns = [col for col in display_columns if col in combined_df.columns]
    top_profits = combined_df.nlargest(10, 'profit_current')[available_columns]
    
    for _, row in top_profits.iterrows():
        product_info = f" ({row['product']})" if pd.notna(row['product']) else ""
        funding_info = f"{row['funding_apr_1d']:.4f}%" if pd.notna(row['funding_apr_1d']) else "N/A"
        print(f"{row['exchange']} {row['symbol']}{product_info} (tenor: {row['tenor']}): "
              f"Savings APR: {row['apr_current']:.4f}%, "
              f"Funding APR: {funding_info}, "
              f"Total Profit: {row['profit_current']:.4f}%")


if __name__ == "__main__":
    main() 