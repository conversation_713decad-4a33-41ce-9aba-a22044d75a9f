#!/usr/bin/env python3
"""
时间匹配策略配置文件
"""

from enum import Enum
from dataclasses import dataclass
from typing import Optional

class TimeMatchingStrategy(Enum):
    """时间匹配策略枚举"""
    LATEST_ONLY = "latest_only"           # 永远使用最新时间戳
    TIME_AWARE = "time_aware"             # 基于参考时间戳的智能匹配
    STRICT_MATCH = "strict_match"         # 严格时间匹配
    NEAREST_NEIGHBOR = "nearest_neighbor" # 最近邻匹配

@dataclass
class TimeMatchingConfig:
    """时间匹配配置"""
    strategy: TimeMatchingStrategy
    tolerance_hours: float = 6.0          # 时间容忍度（小时）
    max_age_hours: float = 24.0           # 数据最大年龄（小时）
    prefer_recent: bool = True            # 是否优先选择较新的数据
    
    def __post_init__(self):
        """验证配置参数"""
        if self.tolerance_hours < 0:
            raise ValueError("tolerance_hours must be non-negative")
        if self.max_age_hours < 0:
            raise ValueError("max_age_hours must be non-negative")

# 预定义的匹配策略配置
# 策略：以最快更新的快照数据为时间基准
MATCHING_STRATEGIES = {
    # 快照数据是时间基准，要求最严格
    "snapshot_reference": TimeMatchingConfig(
        strategy=TimeMatchingStrategy.LATEST_ONLY,
        tolerance_hours=0.5,   # 快照数据30分钟内必须一致
        max_age_hours=2.0,     # 快照数据最多2小时
        prefer_recent=True
    ),

    # 储蓄数据变化慢，向快照时间对齐，容忍度大
    "savings_to_snapshot": TimeMatchingConfig(
        strategy=TimeMatchingStrategy.TIME_AWARE,
        tolerance_hours=24.0,  # 储蓄数据24小时内都可接受
        max_age_hours=72.0,    # 最多3天的储蓄数据
        prefer_recent=True
    ),

    # 历史资金费率数据，中等容忍度
    "funding_historical": TimeMatchingConfig(
        strategy=TimeMatchingStrategy.TIME_AWARE,
        tolerance_hours=6.0,   # 历史数据6小时内可接受
        max_age_hours=24.0,
        prefer_recent=True
    ),

    # 严格匹配模式（用于测试）
    "strict": TimeMatchingConfig(
        strategy=TimeMatchingStrategy.STRICT_MATCH,
        tolerance_hours=0.1,   # 只允许6分钟误差
        max_age_hours=6.0,
        prefer_recent=True
    )
}

def get_matching_config(data_type: str) -> TimeMatchingConfig:
    """
    获取指定数据类型的匹配配置
    
    Args:
        data_type: 数据类型 ("savings", "snapshot", "funding_rates", "strict")
    
    Returns:
        TimeMatchingConfig: 匹配配置
    """
    return MATCHING_STRATEGIES.get(data_type, MATCHING_STRATEGIES["snapshot"])

def should_use_data(data_timestamp, reference_timestamp, config: TimeMatchingConfig) -> bool:
    """
    判断是否应该使用某个时间戳的数据
    
    Args:
        data_timestamp: 数据时间戳
        reference_timestamp: 参考时间戳
        config: 匹配配置
    
    Returns:
        bool: 是否应该使用该数据
    """
    import pandas as pd
    from datetime import datetime, timedelta
    
    data_dt = pd.to_datetime(data_timestamp)
    ref_dt = pd.to_datetime(reference_timestamp) if reference_timestamp else datetime.now()
    
    # 检查数据年龄
    age_hours = (datetime.now() - data_dt).total_seconds() / 3600
    if age_hours > config.max_age_hours:
        return False
    
    # 根据策略判断
    if config.strategy == TimeMatchingStrategy.LATEST_ONLY:
        return True  # 总是使用最新数据
    
    elif config.strategy == TimeMatchingStrategy.TIME_AWARE:
        time_diff_hours = abs((data_dt - ref_dt).total_seconds()) / 3600
        return time_diff_hours <= config.tolerance_hours
    
    elif config.strategy == TimeMatchingStrategy.STRICT_MATCH:
        time_diff_hours = abs((data_dt - ref_dt).total_seconds()) / 3600
        return time_diff_hours <= config.tolerance_hours
    
    elif config.strategy == TimeMatchingStrategy.NEAREST_NEIGHBOR:
        return True  # 由调用方选择最近的
    
    return False

# 使用示例和说明
USAGE_EXAMPLES = """
使用示例:

1. 获取储蓄数据匹配配置:
   config = get_matching_config("savings")

2. 判断是否使用某个时间戳的数据:
   should_use = should_use_data(
       data_timestamp="2025-07-07 08:00:00",
       reference_timestamp="2025-07-07 10:00:00", 
       config=config
   )

3. 自定义匹配策略:
   custom_config = TimeMatchingConfig(
       strategy=TimeMatchingStrategy.TIME_AWARE,
       tolerance_hours=3.0,
       max_age_hours=12.0
   )

匹配策略说明:

- LATEST_ONLY: 总是使用最新的数据，忽略时间差
- TIME_AWARE: 在容忍范围内选择最接近参考时间的数据
- STRICT_MATCH: 严格要求时间差在容忍范围内
- NEAREST_NEIGHBOR: 选择时间上最接近的数据

数据类型建议 (以快照为时间基准):

- 快照数据: 作为时间基准，要求最严格 (30分钟容忍度)
- 储蓄数据: 变化慢，向快照时间对齐 (24小时容忍度)
- 历史资金费率: 中等要求 (6小时容忍度)

时间基准策略:
- 以更新最快的快照数据时间戳为基准
- 其他数据向快照时间对齐
- 保证数据的时效性和一致性
"""

if __name__ == "__main__":
    print("时间匹配策略配置")
    print("=" * 50)
    
    for name, config in MATCHING_STRATEGIES.items():
        print(f"\n{name.upper()}:")
        print(f"  策略: {config.strategy.value}")
        print(f"  容忍度: {config.tolerance_hours} 小时")
        print(f"  最大年龄: {config.max_age_hours} 小时")
        print(f"  优先最新: {config.prefer_recent}")
    
    print(USAGE_EXAMPLES)
