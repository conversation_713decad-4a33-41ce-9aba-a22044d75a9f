# 资金费率数据拼接方案

## 概述

本方案旨在将 `funding_rates_snapshot.csv` 中的最新资金费率数据拼接到 `saving_funding_info.csv` 中，以提供更准确和及时的套利收益计算。

## 数据源分析

### 1. 现有数据源

| 数据源 | 文件路径 | 更新频率 | 数据特点 |
|--------|----------|----------|----------|
| 储蓄数据 | `historical_data/savings_info_df/savings_info.csv` | 每小时 | 各交易所储蓄产品的APR |
| 历史资金费率 | `historical_data/funding_info_df/funding_info.csv` | 每10分钟 | 统计的历史资金费率APR |
| 资金费率快照 | `snapshot_data/funding_rates_snapshot/funding_rates_snapshot.csv` | 每15秒 | 最新的资金费率快照 |
| 现货快照 | `snapshot_data/spot_snapshot/spot_snapshot.csv` | 每15秒 | 现货价格和成交量 |
| 合约快照 | `snapshot_data/perp_snapshot/perp_snapshot.csv` | 每15秒 | 永续合约价格和成交量 |

### 2. 新增数据源特点

**资金费率快照数据结构：**
```csv
symbol,funding_rate,timestamp,exchange,multiplier,base_symbol
1000CATUSDT,5e-05,2025-07-07 02:13:40,binance,1000,CAT
```

**特点：**
- 所有记录共享同一个时间戳（最新快照时间）
- 包含完整的符号解析信息（multiplier, base_symbol）
- 资金费率为当前实时值，通常每8小时更新一次

## 匹配策略

### 1. 主键匹配方案

**选择方案：使用 `(exchange, base_symbol)` 作为匹配键**

**理由：**
- `base_symbol` 是标准化后的币种符号，匹配准确性高
- 避免了复杂的符号清理和转换逻辑
- 与现有的现货、合约数据匹配策略一致

### 2. 时间匹配策略

**快照数据时间处理：**
1. 获取快照数据中的最新时间戳
2. 过滤出该时间戳的所有记录
3. 对于同一 `(exchange, base_symbol)` 的多条记录，取第一条

**历史数据时间处理：**
- 使用统计后的APR值，无需特殊时间处理

## 实现方案

### 1. 新增字段

| 字段名 | 数据类型 | 说明 |
|--------|----------|------|
| `current_funding_rate` | float | 当前资金费率（来自快照） |
| `funding_rate_timestamp` | datetime | 资金费率快照时间戳 |
| `current_funding_apr` | float | 当前资金费率年化收益率 |
| `effective_funding_rate` | float | 有效资金费率（优先使用当前值） |

### 2. 计算逻辑

**资金费率年化转换：**
```python
current_funding_apr = current_funding_rate * 365 * 3
```
- 假设每天3次资金费率结算（8小时间隔）
- 转换为年化收益率便于比较

**有效资金费率选择：**
```python
effective_funding_rate = current_funding_rate or (funding_apr_1d / 365 / 3)
```
- 优先使用当前快照值
- 如果没有，则使用历史平均值转换

**总收益率计算：**
```python
profit_current = apr_current + current_funding_apr or funding_apr_1d
```
- 优先使用当前资金费率计算
- 如果没有当前值，则使用历史平均值

### 3. 数据处理流程

```mermaid
graph TD
    A[储蓄数据] --> E[合并处理]
    B[现货快照] --> E
    C[合约快照] --> E
    D[历史资金费率] --> E
    F[资金费率快照] --> G[时间过滤]
    G --> H[符号匹配]
    H --> E
    E --> I[计算收益率]
    I --> J[输出结果]
```

## 代码实现

### 1. 新增函数

```python
def load_funding_rates_snapshot():
    """加载资金费率快照数据"""
    # 读取快照文件
    # 过滤最新时间戳
    # 返回处理后的数据
```

### 2. 修改合并逻辑

```python
def combine_savings_with_market_data():
    # 现有逻辑...
    
    # 新增：拼接资金费率快照
    if funding_snapshot_df is not None:
        # 按 (exchange, base_symbol) 分组
        # 左连接到主表
        # 计算年化收益率
```

## 数据质量保证

### 1. 覆盖率监控

- 监控当前资金费率的覆盖率
- 对比历史数据覆盖率
- 输出详细的匹配统计信息

### 2. 数据验证

- 验证时间戳的合理性
- 检查资金费率的数值范围
- 确保计算结果的正确性

## 使用方法

### 1. 运行数据合并

```bash
cd data_collection/arbitrage_functions
python gather_saving_funding_info.py
```

### 2. 测试验证

```bash
python test_funding_rate_integration.py
```

### 3. 查看结果

输出文件：`historical_data/combined_info_df/saving_funding_info.csv`

新增字段将包含最新的资金费率信息，提供更准确的套利收益计算。

## 预期效果

1. **数据时效性提升**：使用15秒更新的快照数据替代10分钟更新的历史统计
2. **计算准确性提升**：使用实时资金费率而非历史平均值
3. **覆盖率提升**：快照数据通常比历史统计数据覆盖更多交易对
4. **决策支持增强**：提供更及时的套利机会识别

## 注意事项

1. 快照数据可能存在短暂的数据缺失
2. 不同交易所的资金费率结算频率可能不同
3. 需要定期验证数据质量和匹配准确性
4. 建议保留历史数据作为备选方案
