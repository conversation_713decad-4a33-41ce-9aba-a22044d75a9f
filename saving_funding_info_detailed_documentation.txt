saving_funding_info.csv 字段详细说明文档
==============================================

文件位置: historical_data/combined_info_df/saving_funding_info.csv
生成脚本: data_collection/arbitrage_functions/gather_saving_funding_info.py

字段详细说明:
=============

1. exchange (交易所)
   - 数据来源: 储蓄数据
   - 源文件: historical_data/savings_info_df/savings_info.csv
   - 说明: 交易所名称，包括 binance, okx, bybit, gateio 等
   - 示例值: "binance", "okx", "bybit"

2. symbol (币种符号)
   - 数据来源: 储蓄数据
   - 源文件: historical_data/savings_info_df/savings_info.csv
   - 说明: 完整的币种符号，可能包含倍数前缀
   - 示例值: "BTC", "1000CAT", "1000CHEEMS"

3. product (产品类型)
   - 数据来源: 储蓄数据
   - 源文件: historical_data/savings_info_df/savings_info.csv
   - 说明: 储蓄产品类型，0D表示活期，30D/60D/90D/120D表示定期
   - 示例值: "0D", "30D", "60D", "90D", "120D"

4. tenor (期限)
   - 数据来源: 储蓄数据
   - 源文件: historical_data/savings_info_df/savings_info.csv
   - 说明: 产品期限，以年为单位，0.0表示活期产品
   - 示例值: 0.0, 0.08219178, 0.16438356, 0.24657534, 0.32876712

5. timestamp (时间戳)
   - 数据来源: 储蓄数据
   - 源文件: historical_data/savings_info_df/savings_info.csv
   - 说明: 数据采集时间戳
   - 示例值: "2025-07-07 08:32:30"

6. apr_current (当前年化收益率)
   - 数据来源: 储蓄数据
   - 源文件: historical_data/savings_info_df/savings_info.csv
   - 说明: 当前储蓄产品的年化收益率
   - 示例值: 0.00916831, 0.01897611

7. apr_1d (1天平均年化收益率)
   - 数据来源: 储蓄数据
   - 源文件: historical_data/savings_info_df/savings_info.csv
   - 说明: 过去1天的平均年化收益率
   - 计算方式: 由 gather_savings_info.py 中的 _calculate_savings_apr 函数计算

8. apr_2d (2天平均年化收益率)
   - 数据来源: 储蓄数据
   - 源文件: historical_data/savings_info_df/savings_info.csv
   - 说明: 过去2天的平均年化收益率

9. apr_5d (5天平均年化收益率)
   - 数据来源: 储蓄数据
   - 源文件: historical_data/savings_info_df/savings_info.csv
   - 说明: 过去5天的平均年化收益率

10. apr_10d (10天平均年化收益率)
    - 数据来源: 储蓄数据
    - 源文件: historical_data/savings_info_df/savings_info.csv
    - 说明: 过去10天的平均年化收益率

11. multiplier (币种倍数)
    - 数据来源: 符号解析函数
    - 源代码: get_root_symbol.py 中的 add_base_symbol_multiplier 函数
    - 说明: 币种的倍数，用于处理如1000CAT这样的符号
    - 示例值: 1, 1000, 10000, 100000, 1000000

12. base_symbol (基础币种符号)
    - 数据来源: 符号解析函数
    - 源代码: get_root_symbol.py 中的 add_base_symbol_multiplier 函数
    - 说明: 去除倍数前缀后的基础币种符号
    - 示例值: "CAT", "CHEEMS", "BTC", "ETH"

13. last (现货最新价格)
    - 数据来源: 现货快照数据
    - 源文件: snapshot_data/spot_snapshot/spot_snapshot.csv
    - 说明: 现货市场的最新成交价格
    - 匹配方式: 通过 exchange + base_symbol 匹配

14. turnover_24h (现货24小时成交额)
    - 数据来源: 现货快照数据
    - 源文件: snapshot_data/spot_snapshot/spot_snapshot.csv
    - 说明: 现货市场24小时成交额
    - 匹配方式: 通过 exchange + base_symbol 匹配

15. perp_last (永续合约最新价格)
    - 数据来源: 永续合约快照数据
    - 源文件: snapshot_data/perp_snapshot/perp_snapshot.csv
    - 说明: 永续合约的最新成交价格
    - 匹配方式: 通过 exchange + base_symbol 匹配

16. perp_turnover_24h (永续合约24小时成交额)
    - 数据来源: 永续合约快照数据
    - 源文件: snapshot_data/perp_snapshot/perp_snapshot.csv
    - 说明: 永续合约24小时成交额
    - 匹配方式: 通过 exchange + base_symbol 匹配

17. funding_apr_1d (1天资金费率年化收益率)
    - 数据来源: 资金费率数据
    - 源文件: historical_data/funding_info_df/funding_info.csv
    - 说明: 过去1天的资金费率年化收益率
    - 匹配方式: 通过 exchange + base_symbol 匹配

18. funding_apr_2d (2天资金费率年化收益率)
    - 数据来源: 资金费率数据
    - 源文件: historical_data/funding_info_df/funding_info.csv
    - 说明: 过去2天的资金费率年化收益率

19. funding_apr_5d (5天资金费率年化收益率)
    - 数据来源: 资金费率数据
    - 源文件: historical_data/funding_info_df/funding_info.csv
    - 说明: 过去5天的资金费率年化收益率

20. funding_apr_10d (10天资金费率年化收益率)
    - 数据来源: 资金费率数据
    - 源文件: historical_data/funding_info_df/funding_info.csv
    - 说明: 过去10天的资金费率年化收益率

21. funding_apr_20d (20天资金费率年化收益率)
    - 数据来源: 资金费率数据
    - 源文件: historical_data/funding_info_df/funding_info.csv
    - 说明: 过去20天的资金费率年化收益率

22. profit_current (总收益率)
    - 数据来源: 计算字段
    - 计算公式: apr_current + funding_apr_1d
    - 说明: 储蓄收益率与资金费率收益率的总和，表示套利机会的总收益
    - 备注: 如果 funding_apr_1d 为空，则使用 0 进行计算

数据处理流程:
=============

1. 储蓄数据处理:
   - 原始数据: historical_data/savings/ 目录下各交易所的CSV文件
   - 处理脚本: data_collection/arbitrage_functions/gather_savings_info.py
   - 输出文件: historical_data/savings_info_df/savings_info.csv

2. 资金费率数据处理:
   - 原始数据: historical_data/funding_rates/ 目录下各交易所的CSV文件
   - 处理脚本: data_collection/arbitrage_functions/gather_funding_info.py
   - 输出文件: historical_data/funding_info_df/funding_info.csv

3. 快照数据:
   - 现货快照: snapshot_data/spot_snapshot/spot_snapshot.csv
   - 合约快照: snapshot_data/perp_snapshot/perp_snapshot.csv

4. 数据合并:
   - 合并脚本: data_collection/arbitrage_functions/gather_saving_funding_info.py
   - 输出文件: historical_data/combined_info_df/saving_funding_info.csv

数据更新频率:
=============
- 储蓄数据: 每小时更新 (HH:05:00)
- 资金费率数据: 每10分钟更新
- 快照数据: 每15秒更新
- 合并数据: 需要手动运行 gather_saving_funding_info.py 脚本

注意事项:
=========
1. 部分字段可能为空值，特别是市场数据字段，因为不是所有币种都有对应的现货或合约交易
2. 资金费率数据通过 base_symbol 匹配，可能存在匹配不上的情况
3. profit_current 字段在 funding_apr_1d 为空时会使用储蓄收益率作为总收益率
