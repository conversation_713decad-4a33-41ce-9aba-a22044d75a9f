字段名,字段来源,来源表/文件,字段说明,,,notes
exchange,储蓄数据,historical_data/savings_info_df/savings_info.csv,交易所名称 (binance/okx/bybit/gateio等),,,前面的字段确实需要来自spot的savings数据
symbol,储蓄数据,historical_data/savings_info_df/savings_info.csv,币种符号 (如BTC/ETH/1000CAT等),,,
product,储蓄数据,historical_data/savings_info_df/savings_info.csv,产品类型 (0D表示活期/30D表示30天定期等),,,
tenor,储蓄数据,historical_data/savings_info_df/savings_info.csv,期限 (以年为单位，0.0表示活期),,,
timestamp,储蓄数据,historical_data/savings_info_df/savings_info.csv,数据时间戳,,,
apr_current,储蓄数据,historical_data/savings_info_df/savings_info.csv,当前年化收益率,,,直接从api获取的，直接就是apr
apr_1d,储蓄数据,historical_data/savings_info_df/savings_info.csv,1天平均年化收益率,,,直接计算的
apr_2d,储蓄数据,historical_data/savings_info_df/savings_info.csv,2天平均年化收益率,,,直接计算的
apr_5d,储蓄数据,historical_data/savings_info_df/savings_info.csv,5天平均年化收益率,,,直接计算的
apr_10d,储蓄数据,historical_data/savings_info_df/savings_info.csv,10天平均年化收益率,,,直接计算的
multiplier,符号解析,get_root_symbol.py函数,币种倍数 (如1000CAT的倍数为1000),,,目的？
base_symbol,符号解析,get_root_symbol.py函数,基础币种符号 (如1000CAT的基础符号为CAT),,,
last,现货数据,snapshot_data/spot_snapshot/spot_snapshot.csv,现货最新价格,,,
turnover_24h,现货数据,snapshot_data/spot_snapshot/spot_snapshot.csv,现货24小时成交额,,,
perp_last,合约数据,snapshot_data/perp_snapshot/perp_snapshot.csv,永续合约最新价格,,,
perp_turnover_24h,合约数据,snapshot_data/perp_snapshot/perp_snapshot.csv,永续合约24小时成交额,,,
funding_apr_1d,资金费率数据,historical_data/funding_info_df/funding_info.csv,1天资金费率年化收益率,,,
funding_apr_2d,资金费率数据,historical_data/funding_info_df/funding_info.csv,2天资金费率年化收益率,,,
funding_apr_5d,资金费率数据,historical_data/funding_info_df/funding_info.csv,5天资金费率年化收益率,,,
funding_apr_10d,资金费率数据,historical_data/funding_info_df/funding_info.csv,10天资金费率年化收益率,,,
funding_apr_20d,资金费率数据,historical_data/funding_info_df/funding_info.csv,20天资金费率年化收益率,,,
profit_current,计算字段,apr_current + funding_apr_1d,总收益率 (储蓄收益+资金费率收益),,,
