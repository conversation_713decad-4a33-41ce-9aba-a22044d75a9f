#!/usr/bin/env python3
"""
测试资金费率数据拼接功能
"""

import sys
import os
import pandas as pd
sys.path.append('data_collection/arbitrage_functions')

from gather_saving_funding_info import main

def test_funding_rate_integration():
    """测试资金费率数据拼接"""
    print("🧪 测试资金费率数据拼接...")
    
    try:
        # 运行主函数
        main()
        
        # 检查输出文件
        import pandas as pd
        from pathlib import Path
        
        output_file = Path("historical_data/combined_info_df/saving_funding_info.csv")
        if output_file.exists():
            df = pd.read_csv(output_file)
            print(f"\n✅ 成功生成合并数据文件，共 {len(df)} 条记录")
            
            # 检查新增字段
            expected_columns = [
                'current_funding_rate', 
                'funding_rate_timestamp', 
                'current_funding_apr',
                'effective_funding_rate'
            ]
            
            missing_columns = [col for col in expected_columns if col not in df.columns]
            if missing_columns:
                print(f"⚠️  缺少字段: {missing_columns}")
            else:
                print("✅ 所有新增字段都存在")
            
            # 检查数据覆盖率
            print(f"\n📊 数据覆盖率统计:")
            print(f"• 当前资金费率覆盖率: {df['current_funding_rate'].notna().sum()}/{len(df)} ({df['current_funding_rate'].notna().sum()/len(df)*100:.1f}%)")
            print(f"• 历史资金费率覆盖率: {df['funding_apr_1d'].notna().sum()}/{len(df)} ({df['funding_apr_1d'].notna().sum()/len(df)*100:.1f}%)")
            print(f"• 总收益率覆盖率: {df['profit_current'].notna().sum()}/{len(df)} ({df['profit_current'].notna().sum()/len(df)*100:.1f}%)")

            # 时间戳匹配质量分析
            if 'funding_rate_timestamp' in df.columns and 'timestamp' in df.columns:
                print(f"\n🕐 时间戳匹配质量分析:")

                # 计算时间差
                df_with_both = df[df['funding_rate_timestamp'].notna() & df['timestamp'].notna()].copy()
                if not df_with_both.empty:
                    df_with_both['savings_time'] = pd.to_datetime(df_with_both['timestamp'])
                    df_with_both['funding_time'] = pd.to_datetime(df_with_both['funding_rate_timestamp'])
                    df_with_both['time_diff_hours'] = abs(
                        (df_with_both['funding_time'] - df_with_both['savings_time']).dt.total_seconds() / 3600
                    )

                    # 匹配质量统计
                    excellent = (df_with_both['time_diff_hours'] <= 1).sum()
                    good = (df_with_both['time_diff_hours'] <= 6).sum() - excellent
                    acceptable = (df_with_both['time_diff_hours'] <= 24).sum() - excellent - good
                    poor = len(df_with_both) - excellent - good - acceptable

                    print(f"• 优秀匹配 (≤1小时): {excellent} ({excellent/len(df_with_both)*100:.1f}%)")
                    print(f"• 良好匹配 (≤6小时): {good} ({good/len(df_with_both)*100:.1f}%)")
                    print(f"• 可接受匹配 (≤24小时): {acceptable} ({acceptable/len(df_with_both)*100:.1f}%)")
                    print(f"• 较差匹配 (>24小时): {poor} ({poor/len(df_with_both)*100:.1f}%)")
                    print(f"• 平均时间差: {df_with_both['time_diff_hours'].mean():.2f} 小时")
                    print(f"• 最大时间差: {df_with_both['time_diff_hours'].max():.2f} 小时")
            
            # 显示样本数据
            print(f"\n📋 样本数据 (前5条有当前资金费率的记录):")
            sample_data = df[df['current_funding_rate'].notna()].head()
            if not sample_data.empty:
                display_cols = [
                    'exchange', 'symbol', 'product', 'apr_current', 
                    'current_funding_rate', 'current_funding_apr', 'profit_current'
                ]
                available_cols = [col for col in display_cols if col in sample_data.columns]
                print(sample_data[available_cols].to_string(index=False))
            else:
                print("没有找到有当前资金费率的记录")
            
            # 显示最高收益机会
            print(f"\n🚀 最高收益机会 (前5名):")
            top_profits = df.nlargest(5, 'profit_current')
            profit_cols = [
                'exchange', 'symbol', 'product', 'apr_current', 
                'current_funding_rate', 'funding_apr_1d', 'profit_current'
            ]
            available_profit_cols = [col for col in profit_cols if col in top_profits.columns]
            print(top_profits[available_profit_cols].to_string(index=False))
            
            return df
        else:
            print("❌ 输出文件不存在")
            return None
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_funding_rate_integration()
