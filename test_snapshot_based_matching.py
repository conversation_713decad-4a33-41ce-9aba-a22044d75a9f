#!/usr/bin/env python3
"""
测试基于快照时间基准的数据匹配策略
"""

import sys
import os
import pandas as pd
from pathlib import Path
sys.path.append('data_collection/arbitrage_functions')

def analyze_timestamp_distribution():
    """分析各数据源的时间戳分布"""
    print("📊 数据源时间戳分析")
    print("=" * 50)
    
    data_sources = [
        ("储蓄数据", "historical_data/savings_info_df/savings_info.csv"),
        ("资金费率快照", "snapshot_data/funding_rates_snapshot/funding_rates_snapshot.csv"),
        ("现货快照", "snapshot_data/spot_snapshot/spot_snapshot.csv"),
        ("合约快照", "snapshot_data/perp_snapshot/perp_snapshot.csv"),
    ]
    
    timestamps_info = {}
    
    for name, file_path in data_sources:
        if Path(file_path).exists():
            try:
                df = pd.read_csv(file_path)
                if 'timestamp' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    latest_ts = df['timestamp'].max()
                    earliest_ts = df['timestamp'].min()
                    unique_count = df['timestamp'].nunique()
                    
                    timestamps_info[name] = {
                        'latest': latest_ts,
                        'earliest': earliest_ts,
                        'unique_count': unique_count,
                        'total_records': len(df)
                    }
                    
                    print(f"\n{name}:")
                    print(f"  📁 文件: {file_path}")
                    print(f"  📊 记录数: {len(df):,}")
                    print(f"  🕐 最新时间: {latest_ts}")
                    print(f"  🕐 最早时间: {earliest_ts}")
                    print(f"  🔢 唯一时间戳: {unique_count}")
                    
                    if unique_count == 1:
                        print(f"  ✅ 所有记录使用相同时间戳")
                    else:
                        print(f"  ⚠️  包含多个时间戳")
                else:
                    print(f"\n{name}: ❌ 没有timestamp列")
            except Exception as e:
                print(f"\n{name}: ❌ 读取失败 - {e}")
        else:
            print(f"\n{name}: ❌ 文件不存在 - {file_path}")
    
    return timestamps_info

def determine_time_reference(timestamps_info):
    """确定时间基准"""
    print(f"\n🎯 确定时间基准")
    print("=" * 30)
    
    # 快照数据源（更新最快）
    snapshot_sources = ["资金费率快照", "现货快照", "合约快照"]
    
    latest_snapshot_time = None
    reference_source = None
    
    for source in snapshot_sources:
        if source in timestamps_info:
            latest_time = timestamps_info[source]['latest']
            if latest_snapshot_time is None or latest_time > latest_snapshot_time:
                latest_snapshot_time = latest_time
                reference_source = source
    
    if latest_snapshot_time:
        print(f"⚡️ 时间基准: {reference_source}")
        print(f"📅 基准时间戳: {latest_snapshot_time}")
        
        # 分析其他数据源与基准的时间差
        print(f"\n🕐 时间差分析:")
        for source, info in timestamps_info.items():
            if source != reference_source:
                time_diff = abs((info['latest'] - latest_snapshot_time).total_seconds() / 3600)
                print(f"  {source}: {time_diff:.2f} 小时")
                
                if time_diff <= 1:
                    print(f"    ✅ 优秀匹配 (≤1小时)")
                elif time_diff <= 6:
                    print(f"    🟡 良好匹配 (≤6小时)")
                elif time_diff <= 24:
                    print(f"    🟠 可接受匹配 (≤24小时)")
                else:
                    print(f"    🔴 较差匹配 (>24小时)")
        
        return latest_snapshot_time, reference_source
    else:
        print("❌ 没有找到快照数据")
        return None, None

def test_new_matching_strategy():
    """测试新的匹配策略"""
    print("🧪 测试基于快照的时间匹配策略")
    print("=" * 60)
    
    # 分析时间戳分布
    timestamps_info = analyze_timestamp_distribution()
    
    # 确定时间基准
    reference_time, reference_source = determine_time_reference(timestamps_info)
    
    if reference_time:
        print(f"\n🚀 策略验证:")
        print(f"✅ 时间基准确定: {reference_source} - {reference_time}")
        print(f"✅ 策略: 以最快更新的快照数据为基准")
        print(f"✅ 其他数据向基准时间对齐")
        
        # 建议的容忍度
        print(f"\n📋 建议的时间容忍度:")
        print(f"  快照数据: 30分钟 (必须时间一致)")
        print(f"  储蓄数据: 24小时 (变化慢，可容忍)")
        print(f"  历史数据: 6小时 (中等要求)")
        
        return True
    else:
        print(f"\n❌ 策略验证失败: 无法确定时间基准")
        return False

def run_integration_test():
    """运行集成测试"""
    print(f"\n🔧 运行数据集成测试...")
    
    try:
        from gather_saving_funding_info import main
        print("✅ 导入成功，运行数据合并...")
        main()
        print("✅ 数据合并完成")
        
        # 检查输出结果
        output_file = Path("historical_data/combined_info_df/saving_funding_info.csv")
        if output_file.exists():
            df = pd.read_csv(output_file)
            print(f"✅ 输出文件生成成功: {len(df)} 条记录")
            
            # 检查时间戳一致性
            if 'funding_rate_timestamp' in df.columns:
                unique_funding_ts = df['funding_rate_timestamp'].nunique()
                print(f"📊 资金费率时间戳唯一值: {unique_funding_ts}")
                
                if unique_funding_ts <= 2:  # 允许少量不同时间戳
                    print("✅ 时间戳一致性良好")
                else:
                    print("⚠️  时间戳一致性需要改进")
            
            return True
        else:
            print("❌ 输出文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 基于快照时间基准的匹配策略测试")
    print("=" * 80)
    
    # 测试策略
    strategy_ok = test_new_matching_strategy()
    
    if strategy_ok:
        # 运行集成测试
        integration_ok = run_integration_test()
        
        if integration_ok:
            print(f"\n🎉 所有测试通过！")
            print(f"✅ 时间基准策略: 以快照数据为准")
            print(f"✅ 数据集成: 成功")
            print(f"✅ 时效性: 保证最新")
        else:
            print(f"\n⚠️  策略正确但集成测试失败")
    else:
        print(f"\n❌ 策略测试失败")
